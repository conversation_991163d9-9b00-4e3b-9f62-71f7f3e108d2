# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/MotorTest/mc02_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/MotorTest/mc02_test/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles /home/<USER>/MotorTest/mc02_test/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mc02_test

# Build rule for target.
mc02_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mc02_test
.PHONY : mc02_test

# fast build rule for target.
mc02_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/build
.PHONY : mc02_test/fast

#=============================================================================
# Target rules for targets named STM32_Drivers

# Build rule for target.
STM32_Drivers: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 STM32_Drivers
.PHONY : STM32_Drivers

# fast build rule for target.
STM32_Drivers/fast:
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build
.PHONY : STM32_Drivers/fast

Core/Src/fdcan.o: Core/Src/fdcan.c.o
.PHONY : Core/Src/fdcan.o

# target to build an object file
Core/Src/fdcan.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.o
.PHONY : Core/Src/fdcan.c.o

Core/Src/fdcan.i: Core/Src/fdcan.c.i
.PHONY : Core/Src/fdcan.i

# target to preprocess a source file
Core/Src/fdcan.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.i
.PHONY : Core/Src/fdcan.c.i

Core/Src/fdcan.s: Core/Src/fdcan.c.s
.PHONY : Core/Src/fdcan.s

# target to generate assembly for a file
Core/Src/fdcan.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.s
.PHONY : Core/Src/fdcan.c.s

Core/Src/gpio.o: Core/Src/gpio.c.o
.PHONY : Core/Src/gpio.o

# target to build an object file
Core/Src/gpio.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.o
.PHONY : Core/Src/gpio.c.o

Core/Src/gpio.i: Core/Src/gpio.c.i
.PHONY : Core/Src/gpio.i

# target to preprocess a source file
Core/Src/gpio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.i
.PHONY : Core/Src/gpio.c.i

Core/Src/gpio.s: Core/Src/gpio.c.s
.PHONY : Core/Src/gpio.s

# target to generate assembly for a file
Core/Src/gpio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.s
.PHONY : Core/Src/gpio.c.s

Core/Src/main.o: Core/Src/main.c.o
.PHONY : Core/Src/main.o

# target to build an object file
Core/Src/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.o
.PHONY : Core/Src/main.c.o

Core/Src/main.i: Core/Src/main.c.i
.PHONY : Core/Src/main.i

# target to preprocess a source file
Core/Src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.i
.PHONY : Core/Src/main.c.i

Core/Src/main.s: Core/Src/main.c.s
.PHONY : Core/Src/main.s

# target to generate assembly for a file
Core/Src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.s
.PHONY : Core/Src/main.c.s

Core/Src/stm32h7xx_hal_msp.o: Core/Src/stm32h7xx_hal_msp.c.o
.PHONY : Core/Src/stm32h7xx_hal_msp.o

# target to build an object file
Core/Src/stm32h7xx_hal_msp.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.o
.PHONY : Core/Src/stm32h7xx_hal_msp.c.o

Core/Src/stm32h7xx_hal_msp.i: Core/Src/stm32h7xx_hal_msp.c.i
.PHONY : Core/Src/stm32h7xx_hal_msp.i

# target to preprocess a source file
Core/Src/stm32h7xx_hal_msp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.i
.PHONY : Core/Src/stm32h7xx_hal_msp.c.i

Core/Src/stm32h7xx_hal_msp.s: Core/Src/stm32h7xx_hal_msp.c.s
.PHONY : Core/Src/stm32h7xx_hal_msp.s

# target to generate assembly for a file
Core/Src/stm32h7xx_hal_msp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.s
.PHONY : Core/Src/stm32h7xx_hal_msp.c.s

Core/Src/stm32h7xx_it.o: Core/Src/stm32h7xx_it.c.o
.PHONY : Core/Src/stm32h7xx_it.o

# target to build an object file
Core/Src/stm32h7xx_it.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.o
.PHONY : Core/Src/stm32h7xx_it.c.o

Core/Src/stm32h7xx_it.i: Core/Src/stm32h7xx_it.c.i
.PHONY : Core/Src/stm32h7xx_it.i

# target to preprocess a source file
Core/Src/stm32h7xx_it.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.i
.PHONY : Core/Src/stm32h7xx_it.c.i

Core/Src/stm32h7xx_it.s: Core/Src/stm32h7xx_it.c.s
.PHONY : Core/Src/stm32h7xx_it.s

# target to generate assembly for a file
Core/Src/stm32h7xx_it.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.s
.PHONY : Core/Src/stm32h7xx_it.c.s

Core/Src/syscalls.o: Core/Src/syscalls.c.o
.PHONY : Core/Src/syscalls.o

# target to build an object file
Core/Src/syscalls.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.o
.PHONY : Core/Src/syscalls.c.o

Core/Src/syscalls.i: Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.i

# target to preprocess a source file
Core/Src/syscalls.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.c.i

Core/Src/syscalls.s: Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.s

# target to generate assembly for a file
Core/Src/syscalls.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.c.s

Core/Src/sysmem.o: Core/Src/sysmem.c.o
.PHONY : Core/Src/sysmem.o

# target to build an object file
Core/Src/sysmem.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.o
.PHONY : Core/Src/sysmem.c.o

Core/Src/sysmem.i: Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.i

# target to preprocess a source file
Core/Src/sysmem.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.c.i

Core/Src/sysmem.s: Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.s

# target to generate assembly for a file
Core/Src/sysmem.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.c.s

startup_stm32h723xx.o: startup_stm32h723xx.s.o
.PHONY : startup_stm32h723xx.o

# target to build an object file
startup_stm32h723xx.s.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.o
.PHONY : startup_stm32h723xx.s.o

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... STM32_Drivers"
	@echo "... mc02_test"
	@echo "... Core/Src/fdcan.o"
	@echo "... Core/Src/fdcan.i"
	@echo "... Core/Src/fdcan.s"
	@echo "... Core/Src/gpio.o"
	@echo "... Core/Src/gpio.i"
	@echo "... Core/Src/gpio.s"
	@echo "... Core/Src/main.o"
	@echo "... Core/Src/main.i"
	@echo "... Core/Src/main.s"
	@echo "... Core/Src/stm32h7xx_hal_msp.o"
	@echo "... Core/Src/stm32h7xx_hal_msp.i"
	@echo "... Core/Src/stm32h7xx_hal_msp.s"
	@echo "... Core/Src/stm32h7xx_it.o"
	@echo "... Core/Src/stm32h7xx_it.i"
	@echo "... Core/Src/stm32h7xx_it.s"
	@echo "... Core/Src/syscalls.o"
	@echo "... Core/Src/syscalls.i"
	@echo "... Core/Src/syscalls.s"
	@echo "... Core/Src/sysmem.o"
	@echo "... Core/Src/sysmem.i"
	@echo "... Core/Src/sysmem.s"
	@echo "... startup_stm32h723xx.o"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

