# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /usr/bin/arm-none-eabi-gcc
C_DEFINES = -DDEBUG -DSTM32H723xx -DUSE_HAL_DRIVER -DUSE_PWR_LDO_SUPPLY

C_INCLUDES = -I/home/<USER>/MotorTest/mc02_test/cmake/stm32cubemx/../../Core/Inc -I/home/<USER>/MotorTest/mc02_test/cmake/stm32cubemx/../../Drivers/STM32H7xx_HAL_Driver/Inc -I/home/<USER>/MotorTest/mc02_test/cmake/stm32cubemx/../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I/home/<USER>/MotorTest/mc02_test/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I/home/<USER>/MotorTest/mc02_test/cmake/stm32cubemx/../../Drivers/CMSIS/Include

C_FLAGS =  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11

