{"version": 3, "configurePresets": [{"name": "default", "hidden": true, "generator": "Unix Makefiles", "binaryDir": "${sourceDir}/build", "toolchainFile": "${sourceDir}/cmake/gcc-arm-none-eabi.cmake", "cacheVariables": {}}, {"name": "Debug", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "Release", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "Debug", "configurePreset": "Debug"}, {"name": "Release", "configurePreset": "Release"}]}