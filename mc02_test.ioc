#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.IPParameters=default_mode_Activation
CORTEX_M7.default_mode_Activation=1
FDCAN1.AutoRetransmission=ENABLE
FDCAN1.CalculateBaudRateNominal=1000000
FDCAN1.CalculateTimeBitNominal=1000
FDCAN1.CalculateTimeQuantumNominal=25.0
FDCAN1.DataPrescaler=3
FDCAN1.DataSyncJumpWidth=10
FDCAN1.DataTimeSeg1=29
FDCAN1.DataTimeSeg2=10
FDCAN1.ExtFiltersNbr=0
FDCAN1.FrameFormat=FDCAN_FRAME_CLASSIC
FDCAN1.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal,FrameFormat,AutoRetransmission,ProtocolException,NominalSyncJumpWidth,DataSyncJumpWidth,DataTimeSeg1,DataTimeSeg2,StdFiltersNbr,<PERSON>t<PERSON><PERSON>ers<PERSON>br,RxFifo0ElmtsNbr,RxFifo1ElmtsNbr,Rx<PERSON>uffersNbr,TxEventsNbr,TxBuffersNbr,TxFifoQueueElmtsNbr,NominalPrescaler,NominalTimeSeg1,NominalTimeSeg2,DataPrescaler
FDCAN1.NominalPrescaler=3
FDCAN1.NominalSyncJumpWidth=10
FDCAN1.NominalTimeSeg1=29
FDCAN1.NominalTimeSeg2=10
FDCAN1.ProtocolException=ENABLE
FDCAN1.RxBuffersNbr=0
FDCAN1.RxFifo0ElmtsNbr=3
FDCAN1.RxFifo1ElmtsNbr=0
FDCAN1.StdFiltersNbr=1
FDCAN1.TxBuffersNbr=0
FDCAN1.TxEventsNbr=0
FDCAN1.TxFifoQueueElmtsNbr=3
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
MMTAppRegionsCount=0
MMTConfigApplied=false
Mcu.CPN=STM32H723VGT6
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M7
Mcu.IP1=FDCAN1
Mcu.IP2=MEMORYMAP
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IPNb=6
Mcu.Name=STM32H723VGTx
Mcu.Package=LQFP100
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin2=PD0
Mcu.Pin3=PD1
Mcu.Pin4=VP_SYS_VS_Systick
Mcu.Pin5=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.PinsNb=6
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H723VGTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.FDCAN1_IT0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PD0.Locked=true
PD0.Mode=FDCAN_Activate
PD0.Signal=FDCAN1_RX
PD1.Locked=true
PD1.Mode=FDCAN_Activate
PD1.Signal=FDCAN1_TX
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H723VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.12.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=mc02_test.ioc
ProjectManager.ProjectName=mc02_test
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=CMake
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_FDCAN1_Init-FDCAN1-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.ADCFreq_Value=100000000
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=2
RCC.DIVM2=24
RCC.DIVN1=40
RCC.DIVN2=200
RCC.DIVP1=1
RCC.DIVP1Freq_Value=*********
RCC.DIVP2Freq_Value=100000000
RCC.DIVP3Freq_Value=48375000
RCC.DIVQ1=4
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=100000000
RCC.DIVQ3Freq_Value=48375000
RCC.DIVR1Freq_Value=*********
RCC.DIVR2Freq_Value=100000000
RCC.DIVR3Freq_Value=48375000
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HSE_VALUE=24000000
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM2,DIVN1,DIVN2,DIVP1,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HSE_VALUE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL2FRACN,PLL3FRACN,PLLFRACN,PLLSourceVirtual,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=48375000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL3FRACN=0
RCC.PLLFRACN=0
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=96750000
RCC.VCOInput1Freq_Value=12000000
RCC.VCOInput2Freq_Value=1000000
RCC.VCOInput3Freq_Value=750000
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
